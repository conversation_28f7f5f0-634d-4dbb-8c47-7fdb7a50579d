<?php

declare(strict_types=1);

return [
    // Standard error messages for different exception types
    'validation' => 'Validation failed',
    'authentication' => 'Authentication required',
    'authorization' => 'Access denied',
    'permission' => 'Insufficient permissions',
    'not_found' => 'Resource not found',
    'method_not_allowed' => 'Method not allowed',
    'throttle' => 'Too many requests',
    'bad_request' => 'Bad request',
    'server_error' => 'Internal server error',
    'service_unavailable' => 'Service temporarily unavailable',
    'generic' => 'An error occurred while processing your request',
    'bad_gateway' => 'Bad gateway',

    // Business logic errors
    'business_logic_error' => 'Business logic error',
    'resource_state_conflict' => 'Resource state conflict',
    'organisation_role_system_guard' => 'Organisation roles cannot use system guard',
    
    // User role related errors
    'role_assignment_failed' => 'Role assignment failed',
    'role_removal_failed' => 'Role removal failed',
    'role_transfer_failed' => 'Role transfer failed',
    
    // Organisation errors
    'organisation_already_suspended' => 'Organisation is already suspended',
    
    // Invitation errors
    'invitation_expired' => 'Invitation has expired',
    'invitation_usage_limit_reached' => 'Invitation usage limit reached',
    'invitation_processing_error' => 'Invitation processing error',
    
    // Email verification errors
    'verification_code_send_error' => 'Failed to send verification code',

    // Permission Service errors
    'permission_current_user_not_owner_or_admin' => 'Current user is not the owner of this organization or a system administrator.',
    'permission_new_owner_must_belong_to_organization' => 'New owner must belong to the specified organization.',
    'permission_current_user_must_belong_to_organization' => 'Current user must belong to the specified organization or be a system administrator.',

    // Invitation Service errors
    'invitation_no_permission_create' => 'You do not have permission to create invitations for this organization',
    'invitation_cannot_create_system_roles' => 'Cannot create invitations for system roles',
    'invitation_email_not_allowed' => 'Your email address is not allowed to accept this invitation',
    'invitation_resource_not_exist' => 'Invitation associated resource does not exist',
    'invitation_unsupported_type' => 'Unsupported invitation type',
    'invitation_processing_error_prefix' => 'Error processing invitation',
    'invitation_role_not_exist' => 'Specified role does not exist',
    'invitation_join_success' => 'Successfully joined organization',
    'invitation_expired' => 'Invitation link has expired',
    'invitation_usage_limit_reached' => 'Invitation link has reached usage limit',

    // User Service errors
    'user_no_access_organization' => 'You do not have access to this organisation.',
    'user_no_access_some_organizations' => 'You do not have access to some of the specified organisations.',
    'user_cannot_remove_owner' => 'Cannot remove organisation owner. Owner role must be transferred first.',
    'user_cannot_remove_owner_with_id' => 'Cannot remove user from organisation ID :id. User is the owner and owner role must be transferred first.',
];
