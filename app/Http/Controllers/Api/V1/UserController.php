<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Constants\ErrorCodes;
use App\Http\Requests\Api\V1\CreateUserRequest;
use App\Http\Requests\Api\V1\GuestUserRegistrationRequest;
use App\Http\Requests\Api\V1\UpdateUserRequest;
use App\Http\Resources\Api\V1\CurrentUserResource;
use App\Http\Resources\Api\V1\UserCollection;
use App\Http\Resources\Api\V1\UserResource;
use App\Models\User;
use App\Services\EmailVerificationService;
use App\Services\PermissionService;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

final class UserController extends ApiController
{
    public function __construct(
        private readonly UserService $userService,
        private readonly PermissionService $permissionService,
        private readonly EmailVerificationService $emailVerificationService
    ) {
        // Apply authorization to all methods except register and sendVerificationCode
        $this->authorizeResource(User::class, 'user', [
            'except' => ['register']
        ]);
    }

    /**
     * Get current authenticated user information with roles.
     */
    public function getCurrentUser(Request $request): JsonResponse
    {
        $user = $request->user();
        $roleInfo = $this->permissionService->getUserCompleteRoleInfo($user);

        // Load organisations relationship and attach role info
        $user->load('organisations');
        $user->roles = $roleInfo;

        return $this->successResponse(
            new CurrentUserResource($user),
            'User information retrieved successfully'
        );
    }

    /**
     * Display a listing of users.
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = (int) $request->get('per_page', 15);
        $organisationId = $request->get('organisation_id') ? (int) $request->get('organisation_id') : null;
        $status = $request->get('status');

        $users = $this->userService->getUsers($perPage, $organisationId, $status, $request->user());

        return $this->successResponse(
            new UserCollection($users),
            'api.user.list_retrieved'
        );
    }

    /**
     * Store a newly created user (authenticated admin users only).
     */
    public function store(CreateUserRequest $request): JsonResponse
    {
        $data = $request->validated();
        $user = $this->userService->create($data, $request->user());

        return $this->successResponse(
            new UserResource($user->load('organisations')),
            'api.user.created',
            201
        );
    }

    /**
     * Register a new user (guest registration).
     */
    public function register(GuestUserRegistrationRequest $request): JsonResponse
    {
        $data = $request->validated();
        $user = $this->userService->create($data, null);

        return $this->successResponse(
            new UserResource($user->load('organisations')),
            'api.user.registered',
            201
        );
    }

    /**
     * Send email verification code for guest user registration.
     */
    public function sendVerificationCode(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email|max:255|unique:users,email',
        ], [
            'email.required' => 'Email address is required',
            'email.email' => 'Email address format is invalid',
            'email.max' => 'Email address cannot exceed 255 characters',
            'email.unique' => 'This email address is already in use',
        ]);

        $email = $request->input('email');

        // Check if we can resend the code (respects retry interval)
        if (!$this->emailVerificationService->canResendCode($email)) {
            return $this->errorResponse(
                'Please wait before requesting a new verification code.',
                null,
                422,
                ErrorCodes::VERIFICATION_CODE_RETRY_LIMIT
            );
        }

        try {
            $code = $this->emailVerificationService->sendVerificationCode($email);

            return $this->successResponse([
                'message' => __('api.user.verification_code_sent_success'),
                'expires_in_seconds' => 900, // 15 minutes - verification code expiry
            ], 'api.user.verification_code_sent');
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                null,
                422,
                ErrorCodes::VERIFICATION_CODE_SEND_ERROR
            );
        }
    }

    /**
     * Display the specified user.
     */
    public function show(Request $request, User $user): JsonResponse
    {
        return $this->successResponse(
            new UserResource($user->load('organisations')),
            'api.user.details_retrieved'
        );
    }

    /**
     * Update the specified user.
     */
    public function update(UpdateUserRequest $request, User $user): JsonResponse
    {
        $data = $request->validated();
        $this->userService->update($user, $data, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            'api.user.updated'
        );
    }

    /**
     * Suspend the specified user.
     */
    public function suspend(Request $request, User $user): JsonResponse
    {
        // Check permission for suspend action
        $this->checkPermission('suspend', $user);

        $this->userService->suspend($user, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            'api.user.suspended'
        );
    }

    /**
     * Add user to organisation.
     */
    public function addToOrganisation(Request $request, int $userId, int $organisationId): JsonResponse
    {
        $user = $this->userService->getById($userId);

        if (!$user) {
            return $this->notFoundResponse('api.user.not_found');
        }

        // Check permission for adding user to organisation
        $this->authorize('addToOrganisation', [$user, $organisationId]);

        $this->userService->addToOrganisation($user, $organisationId, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            'api.user.added_to_organisation'
        );
    }

    /**
     * Remove user from organisation.
     */
    public function removeFromOrganisation(Request $request, int $userId, int $organisationId): JsonResponse
    {
        $user = $this->userService->getById($userId);

        if (!$user) {
            return $this->notFoundResponse('api.user.not_found');
        }

        // Check permission for removing user from organisation
        $this->authorize('removeFromOrganisation', [$user, $organisationId]);

        $this->userService->removeFromOrganisation($user, $organisationId, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            'api.user.removed_from_organisation'
        );
    }

    /**
     * Sync user organisations.
     */
    public function syncOrganisations(Request $request, int $userId): JsonResponse
    {
        $user = $this->userService->getById($userId);

        if (!$user) {
            return $this->notFoundResponse('api.user.not_found');
        }

        $organisationIds = $request->input('organisation_ids', []);

        // Check permission for syncing user organisations
        $this->authorize('syncOrganisations', [$user, $organisationIds]);

        $this->userService->syncOrganisations($user, $organisationIds, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            'api.user.organisations_synced'
        );
    }

    /**
     * Activate the specified user.
     */
    public function activate(Request $request, User $user): JsonResponse
    {
        // Check permission for activate action
        $this->checkPermission('activate', $user);

        $this->userService->activate($user, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            'api.user.activated'
        );
    }
}
