<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Constants\ErrorCodes;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

final class UserRoleController extends ApiController
{
    public function __construct(
        private readonly PermissionService $permissionService
    ) {}

    /**
     * Assign a role to a user.
     */
    public function assignRole(Request $request, User $user): JsonResponse
    {
        $request->validate([
            'role_id' => 'required|integer|exists:roles,id',
        ]);

        $role = Role::findOrFail($request->input('role_id'));
        $assigner = $request->user();

        try {
            $this->authorize('assignRole', ['user_role', $user, $role]);

            $this->permissionService->assignRoleToUserSafely($assigner, $user, $role);

            return $this->successResponse([
                'user_id' => $user->id,
                'role' => [
                    'id' => $role->id,
                    'name' => $role->name,
                    'organisation_id' => $role->organisation_id,
                ],
            ], 'api.role.assignment_success');
        } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
            // If authorization fails, still try the business logic validation to get proper error messages
            try {
                $this->permissionService->assignRoleToUserSafely($assigner, $user, $role);
            } catch (ValidationException $ve) {
                return $this->validationErrorResponse($ve->errors(), 'api.role.assignment_failed');
            }

            // If business logic passes but authorization fails, return 403
            throw $e;
        } catch (ValidationException $e) {
            return $this->validationErrorResponse($e->errors(), 'api.role.assignment_failed');
        }
    }

    /**
     * Remove a role from a user.
     */
    public function removeRole(Request $request, User $user, Role $role): JsonResponse
    {
        $remover = $request->user();

        $this->authorize('removeRole', ['user_role', $user, $role]);

        try {
            $this->permissionService->removeRoleFromUser($user, $role);

            return $this->successResponse([
                'user_id' => $user->id,
                'role' => [
                    'id' => $role->id,
                    'name' => $role->name,
                    'organisation_id' => $role->organisation_id,
                ],
            ], 'api.role.removal_success');
        } catch (ValidationException $e) {
            return $this->validationErrorResponse($e->errors(), 'api.role.removal_failed');
        }
    }

    /**
     * Transfer owner role from current owner to another user.
     */
    public function transferOwnerRole(Request $request, User $user): JsonResponse
    {
        $request->validate([
            'organisation_id' => 'required|integer|exists:organisations,id',
        ]);

        $currentOwner = $request->user();
        $organisationId = $request->input('organisation_id');

        // The $user parameter is the new owner (from route model binding)
        $newOwner = $user;

        $this->authorize('transferOwnerRole', ['user_role', $newOwner, $organisationId]);

        try {
            $this->permissionService->transferOwnerRole($currentOwner, $newOwner, $organisationId);

            return $this->successResponse([
                'previous_owner_id' => $currentOwner->id,
                'new_owner_id' => $newOwner->id,
                'organisation_id' => $organisationId,
            ], 'api.role.transfer_success');
        } catch (ValidationException $e) {
            return $this->validationErrorResponse($e->errors(), 'api.role.transfer_failed');
        }
    }

    /**
     * Get roles that the current user can assign.
     */
    public function getAssignableRoles(Request $request): JsonResponse
    {
        $user = $request->user();

        $this->authorize('getAssignableRoles', 'user_role');

        $assignableRoleNames = $this->permissionService->getAssignableRoles($user);

        // Convert role names to objects with name property for API consistency
        $assignableRoles = collect($assignableRoleNames)->map(function ($roleName) {
            return ['name' => $roleName];
        })->values()->toArray();

        return $this->successResponse($assignableRoles, 'api.role.assignable_list_retrieved');
    }

    /**
     * Get a user's roles.
     */
    public function getUserRoles(Request $request, User $user): JsonResponse
    {
        $this->authorize('getUserRoles', ['user_role', $user]);

        $roleInfo = $this->permissionService->getUserCompleteRoleInfo($user);

        return $this->successResponse([
            'user_id' => $user->id,
            'user_name' => $user->name,
            'user_email' => $user->email,
            'organisations' => $user->organisations->map(function ($org) {
                return [
                    'id' => $org->id,
                    'name' => $org->name,
                    'code' => $org->code,
                    'status' => $org->status,
                ];
            }),
            'roles' => $roleInfo,
        ], 'api.role.user_roles_retrieved');
    }
}
