APP_NAME=JastPartnerPortal
APP_ENV=local
APP_KEY=base64:5PTJf/Wffd2zqtLJSKmc/daWeH9C6Y+OzzJvmLbskpk=
APP_DEBUG=true
APP_URL=http://localhost

APP_ADMIN_EMAIL=<EMAIL>
APP_ADMIN_PASSWORD=password

APP_LOCALE=zh
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=zh_CN

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

# Docker specific settings
DOCKER_PHP_VERSION=8.4
DOCKER_MYSQL_VERSION=8.0
DOCKER_REDIS_VERSION=7.0
DOCKER_NGINX_VERSION=1.25

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single,requests
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug
LOG_REQUESTS=true

MAIN_DB_CONNECTION=mysql
MAIN_DB_HOST=mysql
MAIN_DB_PORT=3306
MAIN_DB_DATABASE=laravel_development
MAIN_DB_USERNAME=root
MAIN_DB_PASSWORD=test123
MAIN_DB_ROOT_PASSWORD=test123
# Allow remote connections, dev only!
MYSQL_ROOT_HOST=%
MYSQL_BIND_ADDRESS=0.0.0.0

# Jast Store Database, read only
STORE_DB_CONNECTION=mysql
STORE_DB_HOST=*******
STORE_DB_PORT=3306
STORE_DB_DATABASE=jast_store
STORE_DB_USERNAME=root
STORE_DB_PASSWORD=

# Session configuration for API (minimal usage)
SESSION_DRIVER=array
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

# Cache configuration optimized for API
CACHE_STORE=redis
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

VITE_APP_NAME="${APP_NAME}"
